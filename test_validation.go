package main

import (
	"fmt"
	"regexp"
)

// validateCommand checks if a command contains prohibited file operations
func validateCommand(command string) (bool, string) {
	// List of prohibited patterns for file operations
	prohibitedPatterns := []string{
		`echo\s+.*\s*>\s*\S+`,           // echo content > file
		`echo\s+.*\s*>>\s*\S+`,          // echo content >> file
		`printf\s+.*\s*>\s*\S+`,         // printf content > file
		`printf\s+.*\s*>>\s*\S+`,        // printf content >> file
		`cat\s*>\s*\S+`,                 // cat > file
		`cat\s*>>\s*\S+`,                // cat >> file
		`.*\s*>\s*\S+\.(txt|md|json|yaml|yml|py|js|go|rs|java|cpp|c|h|hpp|sh|bat|ps1|conf|cfg|ini|xml|html|css|sql)`, // any redirection to common file types
		`.*\s*>>\s*\S+\.(txt|md|json|yaml|yml|py|js|go|rs|java|cpp|c|h|hpp|sh|bat|ps1|conf|cfg|ini|xml|html|css|sql)`, // any append to common file types
	}
	
	for _, pattern := range prohibitedPatterns {
		matched, err := regexp.MatchString(pattern, command)
		if err == nil && matched {
			return false, fmt.Sprintf("Command contains prohibited file operation. Use CreateAndApplyDiffs function instead of raw file redirection. Prohibited pattern: %s", pattern)
		}
	}
	
	return true, ""
}

func main() {
	fmt.Println("=== Testing Command Validation ===\n")
	
	testCommands := []struct {
		command     string
		shouldPass  bool
		description string
	}{
		{"ls -la", true, "Basic directory listing"},
		{"git status", true, "Git command"},
		{"echo 'hello world'", true, "Echo without redirection"},
		{"echo 'hello world' > test.txt", false, "Echo with file redirection"},
		{"echo 'hello world' >> test.txt", false, "Echo with file append"},
		{"printf 'hello world' > test.py", false, "Printf with file redirection"},
		{"cat > newfile.js", false, "Cat with file redirection"},
		{"cat existing_file.txt", true, "Cat for reading files"},
		{"ls -la > output.txt", false, "Command with file redirection"},
		{"ls -la | grep test", true, "Pipe operations"},
	}
	
	for i, test := range testCommands {
		fmt.Printf("%d. Testing: %s\n", i+1, test.command)
		fmt.Printf("   Description: %s\n", test.description)
		
		isValid, errorMsg := validateCommand(test.command)
		
		if isValid == test.shouldPass {
			if test.shouldPass {
				fmt.Printf("   ✅ PASS: Command correctly allowed\n")
			} else {
				fmt.Printf("   ✅ PASS: Command correctly blocked\n")
				fmt.Printf("   📝 Reason: %s\n", errorMsg)
			}
		} else {
			if test.shouldPass {
				fmt.Printf("   ❌ FAIL: Command should have been allowed but was blocked\n")
				fmt.Printf("   📝 Error: %s\n", errorMsg)
			} else {
				fmt.Printf("   ❌ FAIL: Command should have been blocked but was allowed\n")
			}
		}
		fmt.Println()
	}
	
	fmt.Println("=== Test Summary ===")
	fmt.Println("This demonstrates the command validation system that prevents")
	fmt.Println("raw file operations and enforces the use of CreateAndApplyDiffs.")
}
