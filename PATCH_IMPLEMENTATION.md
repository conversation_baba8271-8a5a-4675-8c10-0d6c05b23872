# Fix Implementation: Apply Patch Instead of Raw Echo

## Issue Description
The original issue was about enforcing the use of the structured `CreateAndApplyDiffs` function instead of allowing raw echo commands and file redirection operations for file modifications.

## Problem
The Menace CLI system had two conflicting approaches for file operations:
1. **Structured approach**: `CreateAndApplyDiffs` function that provides proper diff tracking, version control, and rollback capabilities
2. **Raw approach**: Basic shell commands like `echo "content" > file.txt` that bypass the structured system

This inconsistency led to:
- No version control for file changes made via raw commands
- No diff tracking or visibility into what changes were made
- No rollback capabilities
- Inconsistent file handling across the application

## Solution Implemented

### 1. Updated System Prompts (`src/llmServer/system_prompt.go`)

**Changes made:**
- Added explicit prohibition of raw file operations in both `getSystemPrompt()` and `getSystemPromptNew()` functions
- Replaced the permissive language about file redirection with strict prohibitions
- Added clear guidance to always use `CreateAndApplyDiffs` for file modifications

**Prohibited operations:**
- `echo "content" > file.txt` (file overwrite)
- `echo "content" >> file.txt` (file append)
- `cat > file.txt` (file overwrite)
- `printf "content" > file.txt` (file overwrite)
- Any command with `>` or `>>` redirection to files
- Any command that directly writes to files without using `CreateAndApplyDiffs`

**Benefits emphasized:**
- Proper diff tracking and version control
- Rollback capabilities
- Clear visibility of what changes are being made
- Consistent file handling across the application

### 2. Added Command Validation (`src/ui/update.go`)

**New function: `validateCommand()`**
- Uses regex patterns to detect prohibited file operations
- Returns validation status and error message
- Covers common file redirection patterns and file extensions

**Validation patterns:**
- `echo` commands with redirection
- `printf` commands with redirection
- `cat` commands with redirection
- Any redirection to common file types (txt, md, json, yaml, py, js, go, etc.)

**Integration points:**
- `CommandSuggestionMsg` case: Validates commands before they're presented to the user
- `SkipStepMsg` case: Validates commands before automatic execution

### 3. Added Comprehensive Tests (`src/ui/update_test.go`)

**Test coverage:**
- Valid commands that should pass (ls, git, echo without redirection)
- Invalid commands that should be blocked (echo with redirection, printf with redirection)
- Edge cases and complex scenarios

**Test scenarios:**
- Basic directory operations (should pass)
- Git operations (should pass)
- Echo with file redirection (should fail)
- Echo without redirection (should pass)
- Printf with file operations (should fail)
- Cat with file operations (should fail)
- Pipe operations (should pass)

## Files Modified

1. **`menace-cli/src/llmServer/system_prompt.go`**
   - Updated both system prompt functions
   - Added explicit prohibitions and guidance

2. **`menace-cli/src/ui/update.go`**
   - Added `validateCommand()` function
   - Integrated validation into command processing pipeline
   - Added regex import for pattern matching

3. **`menace-cli/src/ui/update_test.go`** (new file)
   - Comprehensive test suite for command validation
   - Covers positive and negative test cases

## How It Works

1. **Prevention at the source**: The updated system prompts instruct the AI to never suggest prohibited file operations
2. **Runtime validation**: If prohibited commands somehow get suggested, they're caught and blocked by the validation function
3. **User feedback**: Clear error messages guide users to use the proper `CreateAndApplyDiffs` function
4. **Consistent enforcement**: Validation occurs at both suggestion time and execution time

## Benefits

1. **Consistency**: All file modifications now go through the structured `CreateAndApplyDiffs` system
2. **Traceability**: Every file change is tracked with proper diffs
3. **Safety**: No accidental file overwrites through raw commands
4. **Rollback capability**: Changes can be undone since they're properly tracked
5. **Better UX**: Users see exactly what changes are being made to files

## Testing

The implementation includes a comprehensive test suite that validates:
- Proper blocking of prohibited commands
- Allowing of legitimate commands
- Correct error messaging
- Edge case handling

## Future Enhancements

This implementation provides a solid foundation that could be extended with:
- More sophisticated pattern matching
- Whitelist-based validation for specific use cases
- Integration with version control systems
- Enhanced diff visualization
- Automated rollback capabilities

## Conclusion

This fix successfully addresses the original issue by enforcing the use of structured file operations (`CreateAndApplyDiffs`) instead of raw echo commands, providing better consistency, safety, and traceability for all file modifications in the Menace CLI system.
