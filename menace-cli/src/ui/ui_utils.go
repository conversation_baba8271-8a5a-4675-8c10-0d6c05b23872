package ui

import (
	"encoding/json"
	"strings"

	"github.com/charmbracelet/lipgloss"
)

// LoadingMsg represents a loading animation frame
type LoadingMsg struct {
	Frame int
}

type SkipStepMsg struct {
	Command_to_execute *CommandSuggestionMsg
	Function_to_execute *FunctionCallMsg
}

// LLMResponseMsg represents a message from the LLM
type LLMResponseMsg struct {
	Content string
}

// Represents a command suggestion if the LLM returns one.
type CommandSuggestionMsg struct {
	Command string
	Reason  string
	AwaitingCommandApproval bool
}

// Represents a function call suggestion if the LLM returns one.
type FunctionCallMsg struct {
	Name   string
	AwaitingCommandApproval bool
	Reason string
	Args   map[string]interface{}
}

// SystemMessage represents a system-level message, typically used for conveying
// information or instructions from the system to the user. Unlike LLMResponseMsg,
// which contains responses generated by the language model, SystemMessage is used
// for non-interactive, system-generated content such as status updates or alerts.
type SystemMessage struct {
	Content string
}

// Styles for Menace CLI UI
// Includes "boxes" for chat and input area
var (
	SidebarStyle = lipgloss.NewStyle().
			Width(20).
			PaddingRight(1).
			Foreground(lipgloss.Color("15"))

	HeaderStyle = lipgloss.NewStyle().
			Bold(true).
			Foreground(lipgloss.Color("10")).
			MarginBottom(1)

	ChatStyle = lipgloss.NewStyle().
			Padding(1).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("12"))

	InputStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("6")).
			Bold(true)

	UserStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("2")).
			Bold(true)

	SystemStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#bd93f9")).
			Bold(true)

	LLMStyle = lipgloss.NewStyle().
			Foreground(lipgloss.Color("#8be9fd")).
			Italic(true).
			Bold(true)
	// Style for the block cursor in the input
	CursorStyle = lipgloss.NewStyle().Reverse(true)

	ButtonStyle = lipgloss.NewStyle().
			Width(12). // fixed width for both buttons
			Align(lipgloss.Center).
			Padding(0, 0).
			Margin(0, 0).
			Foreground(lipgloss.Color("#f8f8f2")).
			Bold(true).
			Border(lipgloss.RoundedBorder()).
			BorderForeground(lipgloss.Color("#bd93f9"))

	ThinkingState = "thinking"
)

// parseFunctionCall parses a [FUNCTION_CALL] block from the LLM response and returns a FunctionCallMsg.
// Returns nil if no function call is found or parsing fails.
func parseFunctionCall(response string) *FunctionCallMsg {
	start := strings.Index(response, "[FUNCTION_CALL]")
	end := strings.Index(response, "[/FUNCTION_CALL]")
	if start == -1 || end == -1 {
		return nil
	}
	content := response[start:end]

	reasonStart := strings.Index(content, "Reason:")
	payloadStart := strings.Index(content, "Payload:")
	approvalStart := strings.Index(content, "AwaitingCommandApproval:")
	if reasonStart == -1 || payloadStart == -1 || approvalStart == -1 {
		return nil
	}

	reason := strings.TrimSpace(content[reasonStart+len("Reason:") : approvalStart])
	cmdApproval := strings.TrimSpace(content[approvalStart+len("AwaitingCommandApproval:") : payloadStart]) == "true"
	payload := strings.TrimSpace(content[payloadStart+len("Payload:"):])
	
	// Find the JSON object in the payload
	jsonStart := strings.Index(payload, "{")
	jsonEnd := strings.LastIndex(payload, "}")
	if jsonStart == -1 || jsonEnd == -1 || jsonEnd <= jsonStart {
		return nil
	}
	jsonStr := payload[jsonStart : jsonEnd+1]

	var parsed struct {
		Name string                 `json:"name"`
		Args map[string]interface{} `json:"args"`
		AwaitingCommandApproval bool `json:"AwaitingCommandApproval"`
	}
	if err := json.Unmarshal([]byte(jsonStr), &parsed); err != nil {
		return nil
	}

	return &FunctionCallMsg{
		Name:   parsed.Name,
		Reason: reason,
		AwaitingCommandApproval: cmdApproval,
		Args:   parsed.Args,
	}
}
