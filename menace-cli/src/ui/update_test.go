package ui

import (
	"testing"
)

func TestValidateCommand(t *testing.T) {
	tests := []struct {
		name        string
		command     string
		shouldPass  bool
		description string
	}{
		{
			name:        "Valid ls command",
			command:     "ls -la",
			shouldPass:  true,
			description: "Basic directory listing should be allowed",
		},
		{
			name:        "Valid git command",
			command:     "git status",
			shouldPass:  true,
			description: "Git commands should be allowed",
		},
		{
			name:        "Prohibited echo redirect",
			command:     "echo 'hello world' > test.txt",
			shouldPass:  false,
			description: "Echo with file redirection should be blocked",
		},
		{
			name:        "Prohibited echo append",
			command:     "echo 'hello world' >> test.txt",
			shouldPass:  false,
			description: "Echo with file append should be blocked",
		},
		{
			name:        "Prohibited printf redirect",
			command:     "printf 'hello world' > test.py",
			shouldPass:  false,
			description: "Printf with file redirection should be blocked",
		},
		{
			name:        "Prohibited cat redirect",
			command:     "cat > newfile.js",
			shouldPass:  false,
			description: "Cat with file redirection should be blocked",
		},
		{
			name:        "Valid echo to stdout",
			command:     "echo 'hello world'",
			shouldPass:  true,
			description: "Echo without redirection should be allowed",
		},
		{
			name:        "Valid cat file read",
			command:     "cat existing_file.txt",
			shouldPass:  true,
			description: "Cat for reading files should be allowed",
		},
		{
			name:        "Prohibited complex redirect",
			command:     "ls -la > output.txt",
			shouldPass:  false,
			description: "Any command with file redirection should be blocked",
		},
		{
			name:        "Valid pipe command",
			command:     "ls -la | grep test",
			shouldPass:  true,
			description: "Pipe operations should be allowed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isValid, errorMsg := validateCommand(tt.command)
			
			if isValid != tt.shouldPass {
				if tt.shouldPass {
					t.Errorf("Expected command '%s' to pass validation, but it failed with: %s", tt.command, errorMsg)
				} else {
					t.Errorf("Expected command '%s' to fail validation, but it passed", tt.command)
				}
			}
			
			// If command should fail, ensure we get an error message
			if !tt.shouldPass && errorMsg == "" {
				t.Errorf("Expected error message for invalid command '%s', but got empty string", tt.command)
			}
			
			// If command should pass, ensure we don't get an error message
			if tt.shouldPass && errorMsg != "" {
				t.Errorf("Expected no error message for valid command '%s', but got: %s", tt.command, errorMsg)
			}
		})
	}
}
